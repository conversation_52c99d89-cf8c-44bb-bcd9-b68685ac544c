# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-15
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

"""
时间序列数据插值填补脚本
使用传统插值方法（线性、三次样条、最近邻）填补时间序列数据中的缺失值。
提供多种插值算法选择，支持随机生成缺失值进行测试，
包含填补精度评估和可视化功能，作为深度学习方法的对比基准。
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import argparse

# 设置目录
results_dir = 'inference_results'
os.makedirs(results_dir, exist_ok=True)

def fill_missing_values(data, method='linear'):
    """
    使用插值方法填补缺失值

    参数:
    data: 包含时间和值的DataFrame，其中可能包含NaN值
    method: 插值方法，可选 'linear', 'cubic', 'nearest'

    返回:
    filled_data: 填补缺失值后的DataFrame
    """
    # 复制数据以避免修改原始数据
    filled_data = data.copy()

    # 检查是否有缺失值
    if not filled_data['value'].isna().any():
        print("数据中没有缺失值，无需填补")
        return filled_data

    # 获取非缺失值的索引和值
    non_missing_indices = filled_data[~filled_data['value'].isna()].index
    non_missing_times = filled_data.loc[non_missing_indices, 'time'].values
    non_missing_values = filled_data.loc[non_missing_indices, 'value'].values

    # 创建插值函数
    if method == 'nearest':
        f = interp1d(non_missing_times, non_missing_values, kind=method, bounds_error=False, fill_value='extrapolate')
    else:
        # 对于线性和三次插值，至少需要2个或4个点
        if len(non_missing_indices) < 2 and method == 'linear':
            print("非缺失值点数不足，无法进行线性插值，改用最近邻插值")
            f = interp1d(non_missing_times, non_missing_values, kind='nearest', bounds_error=False, fill_value='extrapolate')
        elif len(non_missing_indices) < 4 and method == 'cubic':
            print("非缺失值点数不足，无法进行三次插值，改用线性插值")
            if len(non_missing_indices) < 2:
                f = interp1d(non_missing_times, non_missing_values, kind='nearest', bounds_error=False, fill_value='extrapolate')
            else:
                f = interp1d(non_missing_times, non_missing_values, kind='linear', bounds_error=False, fill_value='extrapolate')
        else:
            f = interp1d(non_missing_times, non_missing_values, kind=method, bounds_error=False, fill_value='extrapolate')

    # 获取缺失值的索引
    missing_indices = filled_data[filled_data['value'].isna()].index

    # 使用插值函数填补缺失值
    filled_data.loc[missing_indices, 'value'] = f(filled_data.loc[missing_indices, 'time'].values)

    return filled_data

def plot_filled_data(original_data, filled_data, output_file):
    """
    绘制填补缺失值前后的对比图

    参数:
    original_data: 原始数据
    filled_data: 填补缺失值后的数据
    output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 8))

    # 绘制原始数据
    plt.plot(original_data['time'], original_data['value'], 'b-', label='Original Data', alpha=0.5)

    # 绘制填补后的数据
    plt.plot(filled_data['time'], filled_data['value'], 'g-', label='Filled Data')

    # 标记缺失值位置
    missing_indices = original_data[original_data['value'].isna()].index

    # 突出显示填补的值
    plt.scatter(filled_data['time'].iloc[missing_indices],
               filled_data['value'].iloc[missing_indices],
               color='r', s=50, label='Filled Values')

    plt.title('Missing Data Imputation Results')
    plt.xlabel('Time')
    plt.ylabel('Head Acceleration X (m/s²)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(output_file)
    plt.close()

def main(data_file, missing_percentage=None, method='linear'):
    print(f"加载数据文件: {data_file}")
    data = pd.read_csv(data_file)

    # 如果指定了缺失百分比，则随机生成缺失值
    if missing_percentage is not None:
        missing_percentage = float(missing_percentage)
        print(f"随机生成 {missing_percentage}% 的缺失值")

        # 保存原始数据的副本
        original_data = data.copy()

        # 计算要生成的缺失值数量
        n_missing = int(len(data) * missing_percentage / 100)

        # 随机选择索引
        missing_indices = np.random.choice(data.index, size=n_missing, replace=False)

        # 将选定的值设为NaN
        data.loc[missing_indices, 'value'] = np.nan

        # 保存包含缺失值的数据
        base_name = os.path.basename(data_file).split('.')[0]
        missing_data_file = os.path.join(results_dir, f"{base_name}_with_missing.csv")
        data.to_csv(missing_data_file, index=False)
        print(f"包含缺失值的数据已保存到: {missing_data_file}")
    else:
        original_data = data.copy()

    # 填补缺失值
    print(f"使用{method}插值方法填补缺失值...")
    filled_data = fill_missing_values(data, method=method)

    # 保存填补后的数据
    base_name = os.path.basename(data_file).split('.')[0]
    filled_data_file = os.path.join(results_dir, f"{base_name}_filled_{method}.csv")
    filled_data.to_csv(filled_data_file, index=False)
    print(f"填补后的数据已保存到: {filled_data_file}")

    # 绘制填补结果
    plot_file = os.path.join(results_dir, f"{base_name}_filled_{method}.png")
    plot_filled_data(data, filled_data, plot_file)
    print(f"填补结果可视化已保存到: {plot_file}")

    # 计算填补精度（如果有原始值）
    if missing_percentage is not None:
        mse = np.mean((original_data.loc[missing_indices, 'value'].values -
                      filled_data.loc[missing_indices, 'value'].values) ** 2)
        print(f"填补精度 (MSE): {mse:.4f}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='填补时间序列数据中的缺失值')
    parser.add_argument('data_file', type=str, help='包含时间序列数据的CSV文件路径')
    parser.add_argument('--missing_percentage', type=float, help='要随机生成的缺失值百分比', default=None)
    parser.add_argument('--method', type=str, choices=['linear', 'cubic', 'nearest'], default='linear',
                        help='插值方法: linear, cubic, nearest')
    args = parser.parse_args()

    main(args.data_file, args.missing_percentage, args.method)
