# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-16
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新客户异常数据处理与高级LSTM异常检测脚本
专门处理客户提供的新增异常验证数据集（数据集8和9），
使用高级LSTM自编码器模型进行异常检测。
支持自动模型选择（优先使用高级LSTM，回退到标准LSTM），
生成详细的异常检测结果和可视化报告。
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import joblib
import tensorflow as tf
from tqdm import tqdm
import argparse
import glob
import re

# 设置目录
model_dir = 'models'
results_dir = 'results'
customer_data_dir = '客户异常数据-验证模型有效性使用'
output_dir = 'new_customer_data_results'
os.makedirs(output_dir, exist_ok=True)

# 定义参数
SEQUENCE_LENGTH = 100  # 序列长度
STRIDE = 10  # 滑动窗口步长

def read_channel_file(file_path):
    """
    读取通道数据文件

    参数:
    file_path: 文件路径

    返回:
    data: 包含时间和值的DataFrame
    """
    # 读取文件内容
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # 提取元数据
    metadata = {}
    data_start_line = 0

    for i, line in enumerate(lines):
        if ':' in line:
            key, value = line.strip().split(':', 1)
            metadata[key.strip()] = value.strip()
        else:
            data_start_line = i
            break

    # 提取数据值
    values = []
    for i in range(data_start_line, len(lines)):
        line = lines[i].strip()
        if line:
            try:
                values.append(float(line))
            except ValueError:
                print(f"警告: 无法解析行 {i+1}: '{line}'")

    # 创建时间序列
    time_start = float(metadata.get('Time of first sample', '0'))
    num_samples = int(metadata.get('Number of samples', str(len(values))))
    time_step = 0.0001  # 假设采样率为10kHz

    times = np.arange(time_start, time_start + num_samples * time_step, time_step)[:len(values)]

    # 创建DataFrame
    df = pd.DataFrame({
        'time': times,
        'value': values
    })

    # 添加测试ID
    test_id = os.path.basename(file_path)
    df['test_id'] = test_id

    return df

def process_new_customer_data():
    """
    处理新的客户异常数据

    返回:
    all_data: 包含所有数据的DataFrame
    """
    all_data = []

    # 遍历新的子目录 (8和9)
    for subdir in ['8', '9']:
        subdir_path = os.path.join(customer_data_dir, subdir)

        if os.path.isdir(subdir_path):
            channel_dir = os.path.join(subdir_path, 'CHANNEL')

            if os.path.exists(channel_dir):
                # 查找头部X方向加速度通道文件
                head_accel_x_files = []

                # 首先读取通道定义文件
                chn_files = glob.glob(os.path.join(channel_dir, '*.chn'))

                if chn_files:
                    chn_file = chn_files[0]
                    with open(chn_file, 'r') as f:
                        chn_content = f.read()

                    # 查找头部X方向加速度通道
                    head_accel_x_channels = []
                    for line in chn_content.split('\n'):
                        if 'Head Acceleration X' in line:
                            match = re.search(r'Name of channel (\d+)', line)
                            if match:
                                channel_num = match.group(1)
                                head_accel_x_channels.append(channel_num)

                    # 查找对应的数据文件
                    for channel_num in head_accel_x_channels:
                        channel_file = os.path.join(channel_dir, f"{subdir}.{channel_num.zfill(3)}")
                        if os.path.exists(channel_file):
                            head_accel_x_files.append(channel_file)

                # 如果没有找到头部X方向加速度通道，则使用所有数据文件
                if not head_accel_x_files:
                    head_accel_x_files = glob.glob(os.path.join(channel_dir, f"{subdir}.*"))
                    # 排除通道定义文件
                    head_accel_x_files = [f for f in head_accel_x_files if not f.endswith('.chn')]

                # 处理找到的头部X方向加速度通道文件
                for file_path in head_accel_x_files:
                    try:
                        df = read_channel_file(file_path)
                        all_data.append(df)
                        print(f"成功处理文件: {file_path}")
                    except Exception as e:
                        print(f"处理文件 {file_path} 时出错: {str(e)}")

    # 合并所有数据
    if all_data:
        all_data_df = pd.concat(all_data, ignore_index=True)

        # 保存处理后的数据
        output_file = os.path.join(output_dir, 'new_customer_Head_Acceleration_X.csv')
        all_data_df.to_csv(output_file, index=False)
        print(f"所有新客户数据已保存到: {output_file}")

        return all_data_df
    else:
        print("未找到任何头部X方向加速度数据")
        return None

def preprocess_data_for_inference(data, scaler, model_type='advanced_lstm'):
    """
    预处理数据用于推理

    参数:
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象
    model_type: 模型类型

    返回:
    X: 预处理后的输入数据
    sequence_indices: 序列的起始索引
    """
    # 标准化数据
    values_scaled = scaler.transform(data[['value']])
    data['value_scaled'] = values_scaled.flatten()

    # 创建序列数据
    sequences = []
    sequence_indices = []

    # 按测试ID分组处理
    for test_id, group in tqdm(data.groupby('test_id'), desc="创建序列"):
        values = group['value_scaled'].values

        # 使用滑动窗口创建序列
        for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
            seq = values[i:i+SEQUENCE_LENGTH]
            sequences.append(seq)
            sequence_indices.append(i)

    # 转换为numpy数组
    X = np.array(sequences)

    # 根据模型类型调整输入形状
    if model_type == 'lstm' or model_type == 'advanced_lstm':
        X = X.reshape(X.shape[0], X.shape[1], 1)  # 重塑为(样本数, 时间步, 特征数)

    return X, sequence_indices

def detect_anomalies(model, data, scaler, threshold, model_type='advanced_lstm'):
    """
    检测异常

    参数:
    model: 自编码器模型
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象
    threshold: 异常检测阈值
    model_type: 模型类型

    返回:
    anomalies: 包含异常标记的DataFrame
    """
    # 预处理数据
    X, sequence_indices = preprocess_data_for_inference(data, scaler, model_type)

    # 使用模型进行预测
    X_pred = model.predict(X)

    # 计算重构误差
    if model_type == 'lstm' or model_type == 'advanced_lstm':
        errors = np.mean(np.square(X - X_pred), axis=(1, 2))
    else:
        errors = np.mean(np.square(X - X_pred), axis=1)

    # 标记异常
    anomalies = pd.DataFrame({
        'start_idx': sequence_indices,
        'end_idx': [i + SEQUENCE_LENGTH - 1 for i in sequence_indices],
        'reconstruction_error': errors,
        'is_anomaly': errors > threshold
    })

    return anomalies

def plot_anomalies(data, anomalies, output_file):
    """
    绘制异常检测结果

    参数:
    data: 原始数据
    anomalies: 异常检测结果
    output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 8))

    # 绘制原始时间序列
    plt.plot(data['time'], data['value'], label='Original Data')

    # 标记异常区域
    anomaly_sequences = anomalies[anomalies['is_anomaly']]
    for _, row in anomaly_sequences.iterrows():
        start_idx = row['start_idx']
        end_idx = row['end_idx']
        if start_idx < len(data['time']) and end_idx < len(data['time']):
            plt.axvspan(data['time'].iloc[start_idx], data['time'].iloc[end_idx],
                       alpha=0.3, color='red')

    plt.title('Advanced LSTM Anomaly Detection Results')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.grid(True, alpha=0.3)
    plt.savefig(output_file)
    plt.close()

def main():
    # 处理新的客户数据
    customer_data = process_new_customer_data()

    if customer_data is None:
        print("未能处理客户数据，退出")
        return

    # 加载模型和阈值
    model_type = 'advanced_lstm'

    # 检查模型文件
    model_file = os.path.join(model_dir, 'advanced_lstm_autoencoder.h5')
    threshold_file = os.path.join(model_dir, 'advanced_lstm_threshold.npy')
    scaler_file = os.path.join(model_dir, 'advanced_lstm_scaler.pkl')

    if not os.path.exists(model_file):
        print(f"模型文件 {model_file} 不存在，尝试使用LSTM模型")
        model_file = os.path.join(model_dir, 'lstm_autoencoder.h5')
        threshold_file = os.path.join(model_dir, 'lstm_threshold.npy')
        scaler_file = os.path.join(model_dir, 'lstm_scaler.pkl')
        model_type = 'lstm'

    if not os.path.exists(model_file):
        print(f"模型文件 {model_file} 不存在，退出")
        return

    # 加载模型
    print(f"加载模型: {model_file}")
    model = tf.keras.models.load_model(model_file, compile=False)
    model.compile(optimizer='adam', loss='mse')

    # 加载阈值
    print(f"加载阈值: {threshold_file}")
    threshold = np.load(threshold_file)

    # 加载标准化器
    print(f"加载标准化器: {scaler_file}")
    scaler = joblib.load(scaler_file)

    # 按测试ID分组进行异常检测
    for test_id, group in customer_data.groupby('test_id'):
        print(f"处理测试ID: {test_id}")

        # 使用高级LSTM自编码器检测异常
        print(f"使用{model_type}自编码器检测异常...")
        anomalies = detect_anomalies(model, group, scaler, threshold, model_type)

        # 统计异常
        anomaly_count = anomalies['is_anomaly'].sum()
        total_sequences = len(anomalies)
        anomaly_percentage = (anomaly_count / total_sequences) * 100

        print(f"{model_type}: 检测到 {anomaly_count} 个异常序列，占总序列的 {anomaly_percentage:.2f}%")

        # 保存异常检测结果
        result_file = os.path.join(output_dir, f"{test_id}_{model_type}_anomalies.csv")
        anomalies.to_csv(result_file, index=False)
        print(f"异常检测结果已保存到: {result_file}")

        # 绘制异常检测结果
        plot_file = os.path.join(output_dir, f"{test_id}_{model_type}_anomalies.png")
        plot_anomalies(group, anomalies, plot_file)
        print(f"异常检测可视化已保存到: {plot_file}")

    # 生成综合报告
    generate_summary_report(customer_data, model_type)

def generate_summary_report(data, model_type):
    """
    生成综合报告

    参数:
    data: 客户数据
    model_type: 模型类型
    """
    # 创建报告目录
    report_dir = os.path.join(output_dir, 'report')
    os.makedirs(report_dir, exist_ok=True)

    # 读取所有异常检测结果
    results = {}

    for test_id in data['test_id'].unique():
        file = os.path.join(output_dir, f"{test_id}_{model_type}_anomalies.csv")

        if os.path.exists(file):
            anomalies = pd.read_csv(file)
            results[test_id] = {
                'anomaly_count': anomalies['is_anomaly'].sum(),
                'total_sequences': len(anomalies),
                'anomaly_percentage': (anomalies['is_anomaly'].sum() / len(anomalies)) * 100
            }

    # 创建综合报告
    report = []
    report.append(f"# 新客户异常数据检测报告 - {model_type}模型\n")
    report.append("## 异常检测结果\n")
    report.append("| 测试ID | 异常序列数 | 总序列数 | 异常百分比 |\n")
    report.append("|--------|------------|----------|------------|\n")

    for test_id, result in sorted(results.items()):
        report.append(f"| {test_id} | {result['anomaly_count']} | {result['total_sequences']} | {result['anomaly_percentage']:.2f}% |\n")

    # 保存报告
    report_file = os.path.join(report_dir, f'{model_type}_anomaly_detection_report.md')
    with open(report_file, 'w') as f:
        f.writelines(report)

    print(f"综合报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
