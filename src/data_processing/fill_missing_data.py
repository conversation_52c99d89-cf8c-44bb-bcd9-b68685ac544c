# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-15
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

"""
时间序列数据缺失值填补脚本
使用训练好的LSTM自编码器模型填补时间序列数据中的缺失值。
支持随机生成缺失值进行测试，以及对真实缺失数据的填补。
包含填补精度评估和可视化功能。
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import joblib
from tensorflow.keras.models import load_model
from tqdm import tqdm
import argparse

# 设置目录
model_dir = 'models'
results_dir = 'inference_results'
os.makedirs(results_dir, exist_ok=True)

# 定义参数
SEQUENCE_LENGTH = 100  # 序列长度
STRIDE = 1  # 用于填补缺失值时使用更小的步长

def preprocess_data_with_missing(data, scaler):
    """
    预处理包含缺失值的数据

    参数:
    data: 包含时间和值的DataFrame，其中可能包含NaN值
    scaler: 用于标准化的scaler对象

    返回:
    X: 预处理后的输入数据
    missing_indices: 缺失值的索引
    sequence_map: 将序列映射回原始数据的索引
    """
    # 复制数据以避免修改原始数据
    data_copy = data.copy()

    # 找出缺失值的索引
    missing_indices = data_copy[data_copy['value'].isna()].index.tolist()

    # 临时用0填充缺失值以便进行标准化
    data_copy['value'].fillna(0, inplace=True)

    # 标准化数据
    values_scaled = scaler.transform(data_copy[['value']])

    # 创建序列数据
    sequences = []
    sequence_map = []  # 存储每个序列对应的原始数据索引

    values = values_scaled.flatten()

    # 使用滑动窗口创建序列
    for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
        # 检查序列中是否包含缺失值
        seq_indices = list(range(i, i + SEQUENCE_LENGTH))
        seq_missing = [idx for idx in seq_indices if idx in missing_indices]

        # 只处理包含缺失值的序列
        if seq_missing:
            sequences.append(values[i:i+SEQUENCE_LENGTH])
            sequence_map.append((i, seq_missing))

    # 转换为numpy数组
    X = np.array(sequences)
    X = X.reshape(X.shape[0], X.shape[1], 1)

    return X, missing_indices, sequence_map

def fill_missing_values(model, data, scaler, output_file):
    """
    使用自编码器填补缺失值

    参数:
    model: 自编码器模型
    data: 包含时间和值的DataFrame，其中可能包含NaN值
    scaler: 用于标准化的scaler对象
    output_file: 输出文件路径

    返回:
    filled_data: 填补缺失值后的DataFrame
    """
    # 复制数据以避免修改原始数据
    filled_data = data.copy()

    # 检查是否有缺失值
    if not filled_data['value'].isna().any():
        print("数据中没有缺失值，无需填补")
        return filled_data

    # 预处理数据
    X, missing_indices, sequence_map = preprocess_data_with_missing(filled_data, scaler)

    if len(X) == 0:
        print("没有找到包含缺失值的序列，无法填补")
        return filled_data

    # 使用模型进行预测
    X_pred = model.predict(X)

    # 填补缺失值
    for i, (start_idx, missing_idx_list) in enumerate(sequence_map):
        for missing_idx in missing_idx_list:
            # 计算缺失值在序列中的相对位置
            seq_pos = missing_idx - start_idx

            # 使用预测值填补缺失值
            pred_value = X_pred[i, seq_pos, 0]

            # 反标准化
            pred_value_original = scaler.inverse_transform([[pred_value]])[0, 0]

            # 填补缺失值
            filled_data.loc[missing_idx, 'value'] = pred_value_original

    # 保存填补后的数据
    filled_data.to_csv(output_file, index=False)

    return filled_data

def plot_filled_data(original_data, filled_data, missing_indices, output_file):
    """
    绘制填补缺失值前后的对比图

    参数:
    original_data: 原始数据
    filled_data: 填补缺失值后的数据
    missing_indices: 缺失值的索引
    output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 8))

    # 绘制原始数据
    plt.plot(original_data['time'], original_data['value'], 'b-', label='Original Data')

    # 绘制填补后的数据
    plt.plot(filled_data['time'], filled_data['value'], 'g-', label='Filled Data')

    # 标记缺失值位置
    for idx in missing_indices:
        plt.axvline(x=original_data['time'].iloc[idx], color='r', linestyle='--', alpha=0.3)

    # 突出显示填补的值
    plt.scatter(filled_data['time'].iloc[missing_indices],
               filled_data['value'].iloc[missing_indices],
               color='r', s=50, label='Filled Values')

    plt.title('Missing Data Imputation Results')
    plt.xlabel('Time')
    plt.ylabel('Head Acceleration X (m/s²)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(output_file)
    plt.close()

def main(data_file, missing_percentage=None):
    # 加载模型和scaler
    model = load_model(os.path.join(model_dir, 'lstm_autoencoder.h5'))
    scaler = joblib.load(os.path.join(model_dir, 'scaler.pkl'))

    print(f"加载数据文件: {data_file}")
    data = pd.read_csv(data_file)

    # 如果指定了缺失百分比，则随机生成缺失值
    if missing_percentage is not None:
        missing_percentage = float(missing_percentage)
        print(f"随机生成 {missing_percentage}% 的缺失值")

        # 保存原始数据的副本
        original_data = data.copy()

        # 计算要生成的缺失值数量
        n_missing = int(len(data) * missing_percentage / 100)

        # 随机选择索引
        missing_indices = np.random.choice(data.index, size=n_missing, replace=False)

        # 将选定的值设为NaN
        data.loc[missing_indices, 'value'] = np.nan

        # 保存包含缺失值的数据
        base_name = os.path.basename(data_file).split('.')[0]
        missing_data_file = os.path.join(results_dir, f"{base_name}_with_missing.csv")
        data.to_csv(missing_data_file, index=False)
        print(f"包含缺失值的数据已保存到: {missing_data_file}")
    else:
        original_data = data.copy()
        missing_indices = data[data['value'].isna()].index.tolist()

    # 填补缺失值
    print("填补缺失值...")
    base_name = os.path.basename(data_file).split('.')[0]
    filled_data_file = os.path.join(results_dir, f"{base_name}_filled.csv")
    filled_data = fill_missing_values(model, data, scaler, filled_data_file)
    print(f"填补后的数据已保存到: {filled_data_file}")

    # 绘制填补结果
    plot_file = os.path.join(results_dir, f"{base_name}_filled.png")
    plot_filled_data(original_data, filled_data, missing_indices, plot_file)
    print(f"填补结果可视化已保存到: {plot_file}")

    # 计算填补精度（如果有原始值）
    if missing_percentage is not None:
        mse = np.mean((original_data.loc[missing_indices, 'value'].values -
                      filled_data.loc[missing_indices, 'value'].values) ** 2)
        print(f"填补精度 (MSE): {mse:.4f}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='填补时间序列数据中的缺失值')
    parser.add_argument('data_file', type=str, help='包含时间序列数据的CSV文件路径')
    parser.add_argument('--missing_percentage', type=float, help='要随机生成的缺失值百分比', default=None)
    args = parser.parse_args()

    main(args.data_file, args.missing_percentage)
