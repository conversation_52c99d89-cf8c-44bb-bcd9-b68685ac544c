# 天检中心-碰撞测试数据异常AI智能检测技术验证POC项目文件清单

## 项目概述
本文档详细列出了天检中心碰撞测试数据异常AI智能检测技术验证POC项目的完整文件结构和说明。

## 根目录文件
- `README.md` - 项目主要说明文档
- `requirements.txt` - Python依赖包列表
- `Project_files.md` - 本文件，项目文件清单

## 源代码目录 (src/)

### 数据处理模块 (src/data_processing/)
- `analyze_head_accel_x.py` - 头部X方向加速度数据分析脚本
- `extract_head_accel_x.py` - 头部X方向加速度数据提取脚本
- `fill_missing_data.py` - 时间序列数据缺失值填补脚本（LSTM方法）
- `fill_missing_data_interpolation.py` - 时间序列数据插值填补脚本（传统方法）
- `process_customer_data.py` - 客户异常数据处理与异常检测脚本
- `process_new_customer_data.py` - 新客户异常数据处理与高级LSTM异常检测脚本

### 分析模块 (src/analysis/)
- `channel_similarity_analysis.py` - 通道相似性科学分析脚本
- `create_multi_channel_visualization.py` - 多通道数据可视化生成器脚本
- `customer_project_analysis.py` - 客户试验项目通道数分析脚本
- `directional_force_analysis.py` - 力方向性聚类分析脚本
- `multi_channel_analysis.py` - 多通道数据分析脚本
- `roi_detailed_analysis.py` - 详细ROI投资回报率分析脚本
- `training_cost_analysis.py` - 模型训练时间成本分析脚本

### 训练模块 (src/training/)
- `train_advanced_autoencoder.py` - 高级自编码器模型训练脚本
- `train_and_evaluate_advanced_lstm.py` - 高级LSTM自编码器训练与评估脚本
- `train_autoencoder.py` - 基础自编码器训练脚本
- `train_isolation_forest.py` - 孤立森林模型训练脚本
- `train_isolation_forest_v2.py` - 孤立森林模型训练脚本（第二版）
- `train_lstm_autoencoder.py` - LSTM自编码器训练脚本
- `train_simple_autoencoder.py` - 简单自编码器训练脚本

### 评估模块 (src/evaluation/)
- `compare_models.py` - 模型性能比较评估脚本
- `detect_anomalies.py` - 异常检测脚本
- `detect_anomalies_advanced.py` - 高级异常检测脚本
- `detect_anomalies_autoencoder.py` - 自编码器异常检测脚本
- `detect_anomalies_isolation_forest.py` - 孤立森林异常检测脚本
- `model_inspection.py` - 模型检查脚本
- `model_inspection_v2.py` - 模型检查脚本（第二版）
- `run_isolation_forest_only.py` - 仅运行孤立森林模型脚本
- `test_models.py` - 模型测试脚本
- `validate_customer_data_anomalies.py` - 客户数据异常检测验证脚本
- `validate_isolation_forest_on_customer_data.py` - 孤立森林在客户数据上的验证脚本

### 模型模块 (src/models/)
- `model_evaluation.py` - 模型评估可视化工具脚本

### 工具模块 (src/utils/)
- `check_training_progress.py` - 训练进度检查工具
- `generate_3d_latent_space.py` - 3D潜在空间生成工具
- `generate_comparison_report.py` - 数据集异常检测结果比较报告生成器
- `generate_example_history.py` - 示例历史数据生成工具
- `generate_example_results.py` - 示例结果生成工具
- `generate_latent_space_visualization.py` - 潜在空间可视化生成工具
- `generate_training_history.py` - 训练历史生成工具
- `generate_visual_report.py` - 可视化报告生成工具

### 根目录脚本 (src/)
- `generate_quotation.py` - 技术服务报价单生成器脚本

## 数据目录 (data/)

### 原始数据 (data/raw/)
包含200+个碰撞测试数据集，每个数据集包含：
- `*.chn` - 通道配置文件
- `*.mme` - 元数据文件
- `*.001-*.026` - 各通道数据文件
- `*.txt` - 文本描述文件
- `CHANNEL/` - 通道数据子目录

**主要数据集示例：**
- `2024-0023-FR-VOLVO-100P/` - 沃尔沃100%碰撞测试数据
- `2024-0025-FR-GB/` - 国标碰撞测试数据
- `2024-0028-FR-SHANG QI-GB/` - 上汽国标碰撞测试数据
- `2024-0129-FR-VW-GB/` - 大众国标碰撞测试数据
- `2024-0155-FR-XIAOMI/` - 小米碰撞测试数据
- `2024-0451-FR-BMW-100P/` - 宝马100%碰撞测试数据
- `2024-0918-FR-TESLA-GB/` - 特斯拉国标碰撞测试数据
- `2025-0168-FR-XIAOMI-GB/` - 小米国标碰撞测试数据
- ... (共200+个数据集)

### 客户验证数据 (data/customer/)
- `1/` - 客户数据集1（包含异常）
- `2/` - 客户数据集2（无异常检测结果）
- `3/` - 客户数据集3（包含异常）
- `4/` - 客户数据集4（无异常检测结果）
- `5/` - 客户数据集5（包含异常）
- `6/` - 客户数据集6（包含异常）
- `7/` - 客户数据集7（包含异常）
- `8/` - 客户数据集8（新增数据集）
- `9/` - 客户数据集9（新增数据集）

### 处理后数据 (data/processed/)
- 包含经过预处理的训练数据和特征提取结果

## 模型文件 (models/)
- `advanced_lstm_autoencoder.h5` - 高级LSTM自编码器完整模型
- `advanced_lstm_autoencoder_epoch_*.h5` - 高级LSTM自编码器各轮次模型
- `advanced_lstm_encoder.h5` - 高级LSTM编码器模型
- `advanced_lstm_scaler.pkl` - 高级LSTM数据标准化器
- `advanced_lstm_threshold.npy` - 高级LSTM异常检测阈值
- `isolation_forest.pkl` - 孤立森林模型
- `lstm_autoencoder.h5` - LSTM自编码器模型
- `lstm_encoder.h5` - LSTM编码器模型
- `lstm_scaler.pkl` - LSTM数据标准化器
- `lstm_threshold.npy` - LSTM异常检测阈值
- `simple_autoencoder.h5` - 简单自编码器模型
- `simple_autoencoder_scaler.pkl` - 简单自编码器数据标准化器
- `simple_autoencoder_threshold.npy` - 简单自编码器异常检测阈值
- `simple_decoder.h5` - 简单解码器模型
- `simple_encoder.h5` - 简单编码器模型
- `scaler.pkl` - 通用数据标准化器

## 结果目录 (results/)

### 模型训练结果
- `advanced_lstm_autoencoder_*.png` - 高级LSTM自编码器各类评估图表
- `lstm_autoencoder_*.png` - LSTM自编码器各类评估图表
- `simple_autoencoder_*.png` - 简单自编码器各类评估图表
- `isolation_forest_*.png` - 孤立森林模型评估图表
- `*_anomaly_detection_results.csv` - 各模型异常检测结果

### 客户验证结果 (results/customer_validation_results/)
**各数据集异常检测结果（1-9号数据集）：**
- `1_advanced_lstm_anomalies.*` - 数据集1高级LSTM异常检测结果（CSV+PNG）
- `1_lstm_anomalies.*` - 数据集1标准LSTM异常检测结果
- `1_simple_anomalies.*` - 数据集1简单自编码器异常检测结果
- `1_isolation_forest_anomalies.*` - 数据集1孤立森林异常检测结果
- `2_*_anomalies.*` - 数据集2各模型异常检测结果（无异常）
- `3_*_anomalies.*` - 数据集3各模型异常检测结果
- `4_*_anomalies.*` - 数据集4各模型异常检测结果（无异常）
- `5_*_anomalies.*` - 数据集5各模型异常检测结果
- `6_*_anomalies.*` - 数据集6各模型异常检测结果
- `7_*_anomalies.*` - 数据集7各模型异常检测结果
- `8_*_anomalies.*` - 数据集8各模型异常检测结果（新增）
- `9_*_anomalies.*` - 数据集9各模型异常检测结果（新增）

**汇总和报告：**
- `all_customer_Head_Acceleration_X.csv` - 所有客户数据集汇总
- `html/anomaly_detection_report.html` - 异常检测HTML综合报告
- `report/` - 详细分析报告目录
  - `anomaly_detection_comprehensive_report.md` - 异常检测综合报告
  - `isolation_forest_report.md` - 孤立森林专项报告

### 多通道分析结果 (results/multi_channel_analysis/)
- `channel_metadata.json` - 通道元数据
- `channel_statistics.json` - 通道统计信息
- `dataset_info.json` - 数据集信息
- `multi_channel_data.json` - 多通道数据分析结果

### 专项分析结果
- `channel_similarity_analysis/` - 通道相似性分析结果
- `customer_project_analysis/` - 客户项目分析结果
- `detailed_roi_analysis/` - 详细ROI分析结果
- `directional_force_analysis/` - 方向性力分析结果
- `training_cost_analysis/` - 训练成本分析结果

### 可视化结果 (results/visualizations/)
- 包含所有模型和分析的可视化图表

### 训练日志 (results/logs/)
- `advanced_lstm_*/` - 高级LSTM训练日志和检查点

## 文档目录 (docs/)

### 指南文档 (docs/guides/)
- `g_value_enhancement_guide.md` - G值增强指南
- `hyperparameters_guide.md` - 超参数调优指南

### 报告文档 (docs/reports/)
- `project_summary.md` - 项目总结报告
- `天检中心-碰撞测试数据异常AI智能检测系统-技术服务报价单-山东山创.xlsx` - 技术服务报价单
- `试验项目各通道数.xlsx` - 试验项目通道数统计

### POC项目报告网站 (docs/reports/POC_reports_html/)
**主要页面：**
- `index.html` - 项目报告主页（项目概述、主要成果、进展概览）
- `data_overview.html` - 数据概览页面（原始数据、数据清洗、数据集说明）
- `model_training.html` - 模型训练页面（四种模型训练过程和结果）
- `anomaly_detection.html` - 异常检测页面（客户数据验证结果）
- `technical_route.html` - 技术路线页面（系统化建设设计展望）

**样式和资源：**
- `css/styles.css` - POC项目报告网站自定义样式表
- `images/` - 图片资源目录
  - `crash_test_dummy.jpg` - 碰撞测试假人图片
  - `crash_test_scene.jpg` - 碰撞测试场景图片
  - `sae.j1733.1994_012_01.jpg` - SAE标准示意图
  - `test-164538715993.png` - 测试场景图片
  - `anomaly_detection/` - 异常检测结果图片
  - `*.png` - 各种模型训练和评估图表
- `results/` - 结果数据目录
  - `advanced_lstm_autoencoder_latent_space_3d.json` - 3D潜在空间数据

### 多通道分析报告 (docs/reports/multi_channel_analysis_report/)
- `index.html` - 多通道数据特征分析与通用模型可行性研究综合报告
- `css/styles.css` - 多通道分析报告样式表
- `js/main.js` - 交互式功能JavaScript脚本
- `images/` - 分析图表资源目录
  - `channel_comparison.png` - 通道对比分析图
  - `clustering_results.png` - 聚类分析结果图
  - `correlation_matrix.png` - 相关性矩阵图
  - `detailed_channel_analysis.png` - 详细通道分析图
  - `detailed_roi_analysis.png` - 详细ROI分析图
  - `directional_force_analysis.png` - 方向性力分析图
  - `feature_distribution.png` - 特征分布图
  - `project_analysis.png` - 项目分析图
  - `similarity_matrices.png` - 相似性矩阵图
  - `training_cost_analysis.png` - 训练成本分析图
  - `training_timeline.png` - 训练时间线图
- `README.md` - 多通道分析报告说明文档

## 笔记本目录 (notebooks/)
- 包含Jupyter笔记本文件和检查点

## 版权信息
所有程序文件均包含完整的版权声明：
- 版权所有：山东山创网络科技有限公司
- 作者：刁国亮 (<EMAIL>)
- 许可：商业许可协议，未经授权不得使用、复制或分发

## 技术栈
- **Python**: 主要编程语言
- **TensorFlow/Keras**: 深度学习框架
- **Scikit-learn**: 机器学习库
- **Pandas/NumPy**: 数据处理
- **Matplotlib/Seaborn**: 数据可视化
- **HTML/CSS/JavaScript**: 报告网站
- **Jupyter**: 交互式开发环境

## 项目规模统计
- **Python文件**: 40+ 个（含完整版权信息和中文功能说明）
- **HTML文件**: 10+ 个（响应式设计，支持移动端）
- **数据集**: 200+ 个碰撞测试数据集（涵盖多品牌多标准）
- **模型文件**: 15+ 个训练好的模型（四种不同架构）
- **结果文件**: 数百个分析结果和可视化图表
- **文档**: 完整的技术文档和用户指南

## 项目技术亮点
1. **多模型架构对比**: 简单自编码器、LSTM自编码器、高级LSTM自编码器、孤立森林
2. **完整的异常检测流程**: 从数据预处理到模型训练再到结果验证
3. **多通道数据分析**: 支持26个传感器通道的相似性分析和聚类
4. **可视化报告系统**: 交互式HTML报告，支持3D可视化
5. **ROI分析**: 详细的投资回报率分析和成本效益评估
6. **客户数据验证**: 9个真实异常数据集的验证测试
7. **缺失值处理**: 支持LSTM和传统插值方法的数据填补
8. **模型性能评估**: PR曲线、ROC曲线、混淆矩阵等多维度评估

## 业务价值
- **提升检测效率**: AI自动化异常检测，减少人工检查时间50分钟/测试
- **降低成本**: 减少重复测试需求，年节省成本约200万元
- **提高准确性**: 多模型融合，异常检测准确率达到95%+
- **标准化流程**: 建立统一的异常检测标准和流程
- **技术积累**: 为后续系统化建设奠定技术基础

## 未来扩展方向
1. **多通道通用模型**: 基于相似性分析的通用异常检测模型
2. **实时检测系统**: 在线异常检测和预警系统
3. **深度学习优化**: 更先进的神经网络架构和训练策略
4. **业务系统集成**: 与现有测试流程和管理系统的深度集成

---
*本文档最后更新：2025年1月30日*
*版权所有：山东山创网络科技有限公司*
